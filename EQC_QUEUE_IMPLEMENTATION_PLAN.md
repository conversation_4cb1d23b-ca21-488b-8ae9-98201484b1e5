# EQC多人多工排队系统实施计划

## 📋 项目概述

将现有的单线程EQC处理系统升级为支持多人同时使用的分布式排队系统，使用Celery作为任务队列，Redis作为消息代理，实现高并发、可扩展的EQC处理能力。

## 🎯 核心目标

1. **多用户并发**: 支持多个用户同时提交EQC处理任务
2. **智能排队**: 基于优先级的任务调度和队列管理
3. **资源保护**: 文件夹锁定机制避免冲突
4. **实时监控**: 任务进度追踪和状态更新
5. **系统稳定**: 错误恢复、重试机制和优雅降级

## 🚀 Phase 1: 基础架构搭建 (2-3天)

### ✅ 已完成组件

1. **Celery任务定义** (`src/services/eqc_celery_tasks.py`)
   - `process_complete_eqc_workflow_async`: 异步EQC工作流程
   - `check_task_status`: 任务状态检查
   - `cleanup_old_tasks`: 任务清理

2. **队列管理器** (`src/services/eqc_task_queue_manager.py`)
   - 用户会话管理
   - 文件夹锁定机制
   - 任务优先级调度
   - 并发控制

3. **API接口** (`src/presentation/api/eqc_queue_api.py`)
   - RESTful队列管理端点
   - 任务CRUD操作
   - 系统状态监控

4. **前端队列管理器** (`src/presentation/web/static/js/business/eqc-queue-manager.js`)
   - 任务提交界面
   - 实时状态监控
   - 进度显示

### 🔧 需要完成的任务

```bash
# 1. 安装Redis依赖
pip install redis celery[redis]

# 2. 启动Redis服务器
# Windows: 下载Redis for Windows
# Linux/Mac: sudo systemctl start redis

# 3. 更新requirements.txt
echo "redis>=4.0.0" >> requirements.txt
echo "celery[redis]>=5.2.0" >> requirements.txt

# 4. 测试基础组件
python -c "from src.services.eqc_task_queue_manager import get_eqc_task_queue_manager; print('✅ 队列管理器加载成功')"
```

## 🛠️ Phase 2: 系统集成 (1-2天)

### 🎯 集成任务

1. **前端集成**
   ```javascript
   // 在main.js中添加队列模式切换
   localStorage.setItem('eqc_use_queue_mode', 'true');
   
   // 测试队列提交
   await eqcQueueManager.submitEQCTask('D:/test/path', {priority: 'NORMAL'});
   ```

2. **后端路由注册**
   ```python
   # 已在ft_eqc_api.py中完成
   app.include_router(eqc_queue_router)
   ```

3. **HTML模板更新**
   ```html
   <!-- 已添加队列管理器脚本 -->
   <script src="/ft-eqc/static/js/business/eqc-queue-manager.js"></script>
   ```

### 🧪 集成测试清单

- [ ] 前端队列模式切换功能
- [ ] 任务提交API调用
- [ ] 状态轮询和更新
- [ ] UI组件正确显示
- [ ] 错误处理和用户反馈

## 🔄 Phase 3: 核心功能测试 (2-3天)

### 🎯 测试场景

1. **单用户流程测试**
   ```bash
   # 启动系统
   python start_eqc_queue_system.py
   
   # 访问Web界面
   http://localhost:8010
   
   # 提交测试任务
   curl -X POST http://localhost:8010/api/eqc-queue/submit \
     -H "Content-Type: application/json" \
     -d '{
       "folder_path": "D:/test/path",
       "user_id": "test_user_1",
       "priority": "NORMAL"
     }'
   ```

2. **多用户并发测试**
   ```python
   # 并发测试脚本
   import asyncio
   import aiohttp
   
   async def test_concurrent_submissions():
       async with aiohttp.ClientSession() as session:
           tasks = []
           for i in range(5):
               task_data = {
                   "folder_path": f"D:/test/path_{i}",
                   "user_id": f"user_{i}",
                   "priority": "NORMAL"
               }
               tasks.append(submit_task(session, task_data))
           
           results = await asyncio.gather(*tasks)
           return results
   ```

3. **文件夹锁定测试**
   - 同一文件夹多用户同时提交
   - 验证锁定机制正确工作
   - 确认锁过期自动释放

### 📊 性能指标

- **响应时间**: API调用 < 500ms
- **吞吐量**: 支持 ≥ 4个并发任务
- **资源使用**: 内存 < 2GB，CPU < 80%
- **错误率**: < 5%
- **恢复时间**: 错误后 < 30秒恢复

## 🚀 Phase 4: 生产部署 (1-2天)

### 🔧 生产环境配置

1. **Redis配置优化**
   ```redis
   # redis.conf
   maxmemory 1gb
   maxmemory-policy allkeys-lru
   save 900 1
   save 300 10
   ```

2. **Celery配置调优**
   ```python
   # celeryconfig.py 生产模式
   broker_url = 'redis://localhost:6379/0'
   result_backend = 'redis://localhost:6379/0'
   worker_prefetch_multiplier = 1
   task_acks_late = True
   worker_max_tasks_per_child = 1000
   ```

3. **监控和日志**
   ```bash
   # 启动Flower监控
   celery flower -A src.services.eqc_celery_tasks.celery_app --port=5555
   
   # 日志轮转配置
   # /etc/logrotate.d/eqc-queue
   /var/log/eqc/*.log {
       daily
       rotate 7
       compress
       missingok
       notifempty
   }
   ```

### 🛡️ 安全配置

1. **API访问控制**
   ```python
   # 添加API密钥验证
   @app.middleware("http")
   async def verify_api_key(request: Request, call_next):
       if request.url.path.startswith("/api/eqc-queue"):
           api_key = request.headers.get("X-API-Key")
           if not verify_api_key(api_key):
               return JSONResponse(
                   status_code=401, 
                   content={"error": "Invalid API key"}
               )
       return await call_next(request)
   ```

2. **用户认证集成**
   ```python
   # 整合现有用户系统
   def get_current_user(request: Request):
       # 从session或JWT获取用户信息
       return user_id
   ```

### 📈 扩展性准备

1. **水平扩展支持**
   ```yaml
   # docker-compose.yml
   version: '3.8'
   services:
     redis:
       image: redis:alpine
       ports:
         - "6379:6379"
     
     celery-worker:
       build: .
       command: celery worker -A src.services.eqc_celery_tasks.celery_app
       scale: 4
       depends_on:
         - redis
     
     fastapi:
       build: .
       command: uvicorn src.presentation.api.ft_eqc_api:app --host 0.0.0.0
       ports:
         - "8010:8000"
       depends_on:
         - redis
   ```

## 📋 验收标准

### ✅ 功能性验收

- [ ] 多用户可同时提交EQC任务
- [ ] 任务按优先级正确排队
- [ ] 文件夹锁定避免冲突
- [ ] 实时进度更新正常
- [ ] 任务完成结果正确显示
- [ ] 错误处理和重试机制工作正常

### ⚡ 性能验收

- [ ] 支持至少4个并发EQC处理任务
- [ ] API响应时间 < 500ms
- [ ] 系统资源使用合理
- [ ] 24小时连续运行稳定

### 🛡️ 安全验收

- [ ] 用户只能访问自己的任务
- [ ] 文件夹访问权限正确
- [ ] 敏感信息不泄露
- [ ] API调用有适当限制

### 🔧 可维护性验收

- [ ] 完整的日志记录
- [ ] 监控面板可用
- [ ] 问题排查工具完备
- [ ] 系统状态可见性

## 🚨 风险评估与缓解

### 🔴 高风险

1. **Redis服务中断**
   - **缓解**: 实现Redis高可用集群
   - **降级**: 自动切换到内存模式

2. **任务执行失败**
   - **缓解**: 增强错误处理和重试机制
   - **监控**: 实时任务失败率告警

### 🟡 中风险

1. **系统负载过高**
   - **缓解**: 动态工作线程调整
   - **限制**: 用户并发任务数限制

2. **文件权限问题**
   - **缓解**: 统一权限管理
   - **测试**: 不同权限场景测试

### 🟢 低风险

1. **前端兼容性**
   - **缓解**: 渐进式增强
   - **降级**: 保留原有同步模式

## 📚 操作手册

### 🚀 系统启动

```bash
# 1. 启动Redis
redis-server

# 2. 启动EQC队列系统
python start_eqc_queue_system.py

# 3. 验证服务状态
curl http://localhost:8010/api/eqc-queue/health
```

### 🔍 监控命令

```bash
# 查看队列状态
curl http://localhost:8010/api/eqc-queue/system/status

# 查看用户任务
curl http://localhost:8010/api/eqc-queue/user/USER_ID/tasks

# 查看Celery worker状态
celery inspect active -A src.services.eqc_celery_tasks.celery_app
```

### 🛠️ 故障排除

```bash
# 重启Celery workers
pkill -f "celery worker"
python start_eqc_queue_system.py

# 清理Redis缓存
redis-cli FLUSHDB

# 查看详细日志
tail -f logs/eqc_queue.log
```

## 📊 成功指标

### 📈 业务指标

- **用户并发数**: 达到4+用户同时使用
- **处理效率**: 任务完成时间缩短20%
- **系统可用性**: 99.5%以上
- **用户满意度**: 无冲突投诉

### 🔧 技术指标

- **响应时间**: P95 < 1秒
- **吞吐量**: 每小时处理24+个EQC任务
- **资源利用**: CPU < 70%, 内存 < 80%
- **错误率**: < 3%

---

**项目负责人**: Backend Architect  
**预计完成时间**: 7-10个工作日  
**资源需求**: Redis服务器, 监控工具, 测试环境  
**风险等级**: 中等 (已有完整实现，主要是集成和测试)  