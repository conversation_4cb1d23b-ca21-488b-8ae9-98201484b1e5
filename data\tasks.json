{"tasks": [{"id": "a18650d3-fdcd-4dc4-b8ec-33dd31495adf", "name": "建立 EQC Celery 任務定義", "description": "在現有 src/tasks.py 檔案中新增 process_complete_eqc_workflow_task Celery 任務，完全重用現有 EQCProcessingService 的 4 步驟方法。包含任務狀態更新、錯誤處理、重試機制，並新增 eqc_queue 佇列配置到 celeryconfig.py", "notes": "關鍵：100% 重用現有邏輯，不重寫任何核心算法。使用 import 方式調用 EQCProcessingService 所有現有方法", "status": "pending", "dependencies": [], "createdAt": "2025-08-02T19:00:40.388Z", "updatedAt": "2025-08-02T19:00:40.388Z", "relatedFiles": [{"path": "src/tasks.py", "type": "TO_MODIFY", "description": "添加新的 EQC 工作流程 Celery 任務", "lineStart": 300, "lineEnd": 350}, {"path": "celeryconfig.py", "type": "TO_MODIFY", "description": "添加 eqc_queue 佇列配置", "lineStart": 64, "lineEnd": 70}, {"path": "src/presentation/api/services/eqc_processing_service.py", "type": "REFERENCE", "description": "重用現有 EQC 處理服務方法", "lineStart": 1, "lineEnd": 500}], "implementationGuide": "1. 在 src/tasks.py 添加新任務：\\n@celery_app.task(bind=True, name='tasks.process_complete_eqc_workflow')\\ndef process_complete_eqc_workflow_task(self, folder_path, user_session_id, options)\\n2. import 現有服務：from src.presentation.api.services.eqc_processing_service import EQCProcessingService\\n3. 實現 4 步驟調用：\\n   - Step1: eqc_service.process_online_eqc()\\n   - Step2: eqc_service.process_eqc_advanced()\\n   - Step3: eqc_service.analyze_eqc_real_data()\\n   - Step4: 結果整理和封裝\\n4. 添加進度更新：self.update_state(state='PROGRESS', meta={...})\\n5. 在 celeryconfig.py 添加：'tasks.process_complete_eqc_workflow': {'queue': 'eqc_queue'}", "verificationCriteria": "1. Celery 任務可成功註冊和發現\\n2. 任務執行時能正確調用所有 4 個步驟\\n3. 進度狀態正確更新到 meta 資訊\\n4. 錯誤處理和重試機制運作正常\\n5. eqc_queue 佇列配置生效", "analysisResult": "將現有同步 processCompleteEQCWorkflow() 改造為支援多人多工的 Celery 排隊系統。核心策略：最大重用現有程式碼（import 方式），整合現有 Celery 基礎設施，實現會話隔離機制，保持4步驟工作流程完整性，提供企業級可靠性保障。技術目標：支援20+並發用戶，任務成功率>95%，處理時間8-12分鐘，平滑降級機制。"}, {"id": "7a52bfc1-f14f-457a-a561-7abf8ea13687", "name": "建立 EQC 會話管理器", "description": "創建 EQCSessionManager 類別實現多人隔離機制，管理用戶會話、任務狀態、結果快取。確保多人同時使用不會互相干擾，提供會話創建、狀態查詢、結果管理功能", "notes": "參考 concurrent_task_manager.py 的模式，實現類似的會話管理機制", "status": "pending", "dependencies": [{"taskId": "a18650d3-fdcd-4dc4-b8ec-33dd31495adf"}], "createdAt": "2025-08-02T19:00:40.388Z", "updatedAt": "2025-08-02T19:00:40.388Z", "relatedFiles": [{"path": "src/services/eqc_session_manager.py", "type": "CREATE", "description": "新建 EQC 會話管理器模組", "lineStart": 1, "lineEnd": 200}, {"path": "src/services/concurrent_task_manager.py", "type": "REFERENCE", "description": "參考現有任務管理器實現模式", "lineStart": 30, "lineEnd": 100}], "implementationGuide": "1. 創建 src/services/eqc_session_manager.py：\\nclass EQCSessionManager:\\n    def __init__(self):\\n        self.active_sessions = {}\\n        self.task_results = {}\\n        self.session_lock = threading.RLock()\\n    \\n    def create_session(self, user_id, folder_path) -> str\\n    def get_session_status(self, session_id) -> Dict\\n    def update_session_status(self, session_id, status)\\n    def cleanup_expired_sessions(self)\\n2. 整合會話與任務 ID 映射\\n3. 實現結果快取機制（限時保存）\\n4. 添加 get_eqc_session_manager() 單例函數", "verificationCriteria": "1. 會話創建生成唯一 session_id\\n2. 多個用戶會話互不干擾\\n3. 會話狀態正確追蹤任務進度\\n4. 過期會話自動清理\\n5. 線程安全的會話管理", "analysisResult": "將現有同步 processCompleteEQCWorkflow() 改造為支援多人多工的 Celery 排隊系統。核心策略：最大重用現有程式碼（import 方式），整合現有 Celery 基礎設施，實現會話隔離機制，保持4步驟工作流程完整性，提供企業級可靠性保障。技術目標：支援20+並發用戶，任務成功率>95%，處理時間8-12分鐘，平滑降級機制。"}, {"id": "8ff4e9f1-79a1-45b1-9e09-e94579b909a6", "name": "新增異步 EQC API 端點", "description": "在 ft_eqc_api.py 中新增異步 API 端點：submit_eqc_workflow_async (提交任務) 和 get_eqc_task_status (查詢狀態)。完全復用 concurrent_tasks_api.py 的 API 模式和錯誤處理邏輯", "notes": "100% 復用 concurrent_tasks_api.py 的成熟模式，確保 API 一致性和穩定性", "status": "pending", "dependencies": [{"taskId": "a18650d3-fdcd-4dc4-b8ec-33dd31495adf"}, {"taskId": "7a52bfc1-f14f-457a-a561-7abf8ea13687"}], "createdAt": "2025-08-02T19:00:40.388Z", "updatedAt": "2025-08-02T19:00:40.388Z", "relatedFiles": [{"path": "src/presentation/api/ft_eqc_api.py", "type": "TO_MODIFY", "description": "添加異步 EQC API 端點", "lineStart": 350, "lineEnd": 400}, {"path": "src/presentation/api/concurrent_tasks_api.py", "type": "REFERENCE", "description": "復用現有並發任務 API 模式", "lineStart": 25, "lineEnd": 150}, {"path": "src/services/eqc_session_manager.py", "type": "DEPENDENCY", "description": "依賴會話管理器", "lineStart": 1, "lineEnd": 200}], "implementationGuide": "1. 在 src/presentation/api/ft_eqc_api.py 添加端點：\\<EMAIL>('/api/eqc/submit_workflow_async')\\nasync def submit_eqc_workflow_async(folder_path: str = Form(...), ...)\\<EMAIL>('/api/eqc/task_status/{task_id}')\\nasync def get_eqc_task_status(task_id: str)\\n2. 復用 concurrent_tasks_api.py 的模式：\\n   - 相同的參數驗證邏輯\\n   - 相同的錯誤處理方式\\n   - 相同的響應格式\\n3. 整合 EQCSessionManager：\\n   - 創建會話 session_id\\n   - 提交 Celery 任務\\n   - 返回任務 ID 和佇列位置\\n4. 使用 Celery AsyncResult 查詢狀態", "verificationCriteria": "1. API 端點正常註冊和路由\\n2. 任務提交返回正確的 task_id 和 session_id\\n3. 狀態查詢返回實時進度和結果\\n4. 錯誤處理和 HTTP 狀態碼正確\\n5. API 響應格式與現有模式一致", "analysisResult": "將現有同步 processCompleteEQCWorkflow() 改造為支援多人多工的 Celery 排隊系統。核心策略：最大重用現有程式碼（import 方式），整合現有 Celery 基礎設施，實現會話隔離機制，保持4步驟工作流程完整性，提供企業級可靠性保障。技術目標：支援20+並發用戶，任務成功率>95%，處理時間8-12分鐘，平滑降級機制。"}, {"id": "0b8c720e-c5b3-4811-96ce-16b822e08b7d", "name": "改造前端 EQC 處理器支援異步模式", "description": "修改 eqc-processor.js 支援異步/同步雙模式，實現任務狀態輪詢、進度更新、優雅降級機制。保持現有功能完整性，新增排隊狀態顯示和會話管理", "notes": "保持向下相容，現有同步模式作為備用。復用所有現有 UI 組件和進度顯示邏輯", "status": "pending", "dependencies": [{"taskId": "8ff4e9f1-79a1-45b1-9e09-e94579b909a6"}], "createdAt": "2025-08-02T19:00:40.388Z", "updatedAt": "2025-08-02T19:00:40.388Z", "relatedFiles": [{"path": "src/presentation/web/static/js/business/eqc-processor.js", "type": "TO_MODIFY", "description": "改造支援異步/同步雙模式", "lineStart": 30, "lineEnd": 200}, {"path": "src/presentation/web/static/js/core/api-client.js", "type": "TO_MODIFY", "description": "新增異步 EQC API 調用方法", "lineStart": 100, "lineEnd": 150}, {"path": "src/presentation/web/static/js/components/progress-display.js", "type": "REFERENCE", "description": "復用現有進度顯示組件", "lineStart": 1, "lineEnd": 100}], "implementationGuide": "1. 修改 src/presentation/web/static/js/business/eqc-processor.js：\\nclass EQCProcessor {\\n    constructor() {\\n        this.isAsyncMode = true;\\n        this.currentTaskId = null;\\n        this.sessionId = this.generateSessionId();\\n    }\\n    \\n    async processCompleteWorkflow(folderPath, options) {\\n        if (this.isAsyncMode) {\\n            return await this.processCompleteWorkflowAsync(folderPath, options);\\n        } else {\\n            return await this.processCompleteWorkflowSync(folderPath, options);\\n        }\\n    }\\n}\\n2. 實現異步處理方法：\\n   - submitEQCWorkflowAsync() API 調用\\n   - pollTaskStatus() 狀態輪詢\\n   - 復用現有 progressDisplay 進度條\\n3. 添加優雅降級：異步失敗自動切換同步模式\\n4. 新增 ApiClient.submitEQCWorkflowAsync() 和 ApiClient.getEQCTaskStatus() 方法", "verificationCriteria": "1. 異步模式正常提交任務並輪詢狀態\\n2. 進度條正確顯示 4 步驟進度\\n3. 異步失敗時自動降級到同步模式\\n4. 排隊狀態和預估時間正確顯示\\n5. 任務完成後結果正常顯示", "analysisResult": "將現有同步 processCompleteEQCWorkflow() 改造為支援多人多工的 Celery 排隊系統。核心策略：最大重用現有程式碼（import 方式），整合現有 Celery 基礎設施，實現會話隔離機制，保持4步驟工作流程完整性，提供企業級可靠性保障。技術目標：支援20+並發用戶，任務成功率>95%，處理時間8-12分鐘，平滑降級機制。"}, {"id": "6575e4c6-cfe5-4305-9635-86824f33a0de", "name": "整合監控儀表板和 WebSocket 即時更新", "description": "將 EQC 異步任務整合到現有 unified-monitoring-dashboard，添加 EQC 任務監控指標。可選實現 WebSocket 即時進度推送機制，提升用戶體驗", "notes": "優先級：監控儀表板 (必須) > WebSocket (可選)。復用現有 code_comparison 監控邏輯", "status": "pending", "dependencies": [{"taskId": "0b8c720e-c5b3-4811-96ce-16b822e08b7d"}], "createdAt": "2025-08-02T19:00:40.388Z", "updatedAt": "2025-08-02T19:00:40.388Z", "relatedFiles": [{"path": ".kiro/specs/unified-monitoring-dashboard/design.md", "type": "REFERENCE", "description": "參考現有監控儀表板設計", "lineStart": 100, "lineEnd": 200}, {"path": "src/infrastructure/adapters/notification/line_notification_service.py", "type": "REFERENCE", "description": "復用現有通知服務", "lineStart": 1, "lineEnd": 100}, {"path": "src/services/concurrent_task_manager.py", "type": "REFERENCE", "description": "參考任務監控實現", "lineStart": 150, "lineEnd": 250}], "implementationGuide": "1. 修改 unified-monitoring-dashboard 配置：\\n   - 添加 EQC 任務計數器\\n   - 監控 eqc_queue 佇列長度\\n   - 統計 EQC 任務成功率和平均執行時間\\n2. 復用現有監控模式：\\n   - 參考 code_comparison_tasks 監控邏輯\\n   - 使用相同的指標收集方式\\n3. 可選：WebSocket 實現 (如時間允許)：\\n   - 創建 EQC WebSocket 端點\\n   - 實現任務進度即時推送\\n   - 前端 WebSocket 客戶端整合\\n4. 整合 LineNotificationService 告警", "verificationCriteria": "1. 監控儀表板顯示 EQC 任務統計\\n2. 佇列長度和執行時間監控正常\\n3. 任務失敗時觸發告警通知\\n4. (可選) WebSocket 即時進度推送運作\\n5. 監控數據準確性驗證", "analysisResult": "將現有同步 processCompleteEQCWorkflow() 改造為支援多人多工的 Celery 排隊系統。核心策略：最大重用現有程式碼（import 方式），整合現有 Celery 基礎設施，實現會話隔離機制，保持4步驟工作流程完整性，提供企業級可靠性保障。技術目標：支援20+並發用戶，任務成功率>95%，處理時間8-12分鐘，平滑降級機制。"}, {"id": "2bdd0498-3beb-4719-928c-4979970b7442", "name": "多人並發測試和性能調校", "description": "進行多人並發場景測試，驗證會話隔離、任務佇列、資源競爭等關鍵功能。性能測試和調校，確保支援 20+ 並發用戶，任務成功率 >95%", "notes": "重點驗證多人使用無衝突，系統在高負載下的穩定性和性能表現", "status": "pending", "dependencies": [{"taskId": "6575e4c6-cfe5-4305-9635-86824f33a0de"}], "createdAt": "2025-08-02T19:00:40.388Z", "updatedAt": "2025-08-02T19:00:40.388Z", "relatedFiles": [{"path": "tests/integration/test_eqc_concurrent.py", "type": "CREATE", "description": "EQC 並發測試腳本", "lineStart": 1, "lineEnd": 300}, {"path": "tests/performance/eqc_load_test.py", "type": "CREATE", "description": "EQC 性能和負載測試", "lineStart": 1, "lineEnd": 200}, {"path": "celeryconfig.py", "type": "TO_MODIFY", "description": "根據測試結果調校配置", "lineStart": 1, "lineEnd": 80}], "implementationGuide": "1. 並發測試腳本：\\n   - 模擬 10-25 個用戶同時提交 EQC 任務\\n   - 驗證會話隔離：不同用戶結果不混淆\\n   - 測試佇列管理：任務順序和優先級\\n2. 性能基準測試：\\n   - 單任務執行時間：8-12 分鐘範圍\\n   - 並發處理能力：20+ 用戶\\n   - 任務成功率：>95%\\n   - 記憶體和 CPU 使用率\\n3. 壓力測試：\\n   - 極限並發數測試\\n   - 長時間運行穩定性\\n   - 錯誤恢復能力\\n4. 性能調校：\\n   - Celery worker 數量調整\\n   - 佇列配置優化\\n   - 快取策略調整", "verificationCriteria": "1. 20 個並發用戶測試全部通過\\n2. 會話隔離驗證：無資料混淆\\n3. 任務成功率達到 95% 以上\\n4. 平均處理時間在 8-12 分鐘範圍\\n5. 系統資源使用率在合理範圍", "analysisResult": "將現有同步 processCompleteEQCWorkflow() 改造為支援多人多工的 Celery 排隊系統。核心策略：最大重用現有程式碼（import 方式），整合現有 Celery 基礎設施，實現會話隔離機制，保持4步驟工作流程完整性，提供企業級可靠性保障。技術目標：支援20+並發用戶，任務成功率>95%，處理時間8-12分鐘，平滑降級機制。"}, {"id": "3d853a1e-9975-4355-b172-40d4178470b3", "name": "部署指南和運維文檔", "description": "編寫完整的部署指南、運維手冊和故障排除文檔。包含 Celery worker 配置、監控設置、備份恢復策略和常見問題解決方案", "notes": "文檔應包含具體的命令示例和配置檔案範本，便於運維人員操作", "status": "pending", "dependencies": [{"taskId": "2bdd0498-3beb-4719-928c-4979970b7442"}], "createdAt": "2025-08-02T19:00:40.388Z", "updatedAt": "2025-08-02T19:00:40.388Z", "relatedFiles": [{"path": "docs/DEPLOYMENT_GUIDE.md", "type": "CREATE", "description": "EQC 異步系統部署指南", "lineStart": 1, "lineEnd": 200}, {"path": "docs/OPERATION_MANUAL.md", "type": "CREATE", "description": "運維操作手冊", "lineStart": 1, "lineEnd": 150}, {"path": "docs/TROUBLESHOOTING.md", "type": "CREATE", "description": "故障排除指南", "lineStart": 1, "lineEnd": 100}, {"path": "README.md", "type": "TO_MODIFY", "description": "更新專案說明文檔", "lineStart": 200, "lineEnd": 250}, {"path": "CHANGELOG.md", "type": "TO_MODIFY", "description": "記錄版本變更", "lineStart": 1, "lineEnd": 50}], "implementationGuide": "1. 部署指南文檔 (DEPLOYMENT_GUIDE.md)：\\n   - Celery worker 啟動配置\\n   - Redis/RabbitMQ 佇列配置\\n   - 環境變數設定\\n   - 生產環境調校參數\\n2. 運維手冊 (OPERATION_MANUAL.md)：\\n   - 日常監控檢查項目\\n   - 性能指標解讀\\n   - 告警處理流程\\n   - 容量規劃建議\\n3. 故障排除 (TROUBLESHOOTING.md)：\\n   - 常見錯誤和解決方案\\n   - 任務卡住問題處理\\n   - 會話清理和恢復\\n   - 日誌分析方法\\n4. 更新現有 README.md 和 CHANGELOG.md", "verificationCriteria": "1. 部署指南包含完整的安裝和配置步驟\\n2. 運維手冊涵蓋日常操作和監控要點\\n3. 故障排除文檔包含常見問題和解決方案\\n4. 文檔內容準確且易於理解\\n5. 所有配置範例經過驗證可用", "analysisResult": "將現有同步 processCompleteEQCWorkflow() 改造為支援多人多工的 Celery 排隊系統。核心策略：最大重用現有程式碼（import 方式），整合現有 Celery 基礎設施，實現會話隔離機制，保持4步驟工作流程完整性，提供企業級可靠性保障。技術目標：支援20+並發用戶，任務成功率>95%，處理時間8-12分鐘，平滑降級機制。"}]}