# FastAPI 依賴注入開發者指南

**版本**: v2.0  
**更新時間**: 2025-08-02  
**適用範圍**: 開發團隊、維護人員、新加入成員  

## 📋 目錄

1. [概述](#概述)
2. [核心概念](#核心概念)
3. [依賴注入模式](#依賴注入模式)
4. [錯誤處理機制](#錯誤處理機制)
5. [測試策略](#測試策略)
6. [最佳實踐](#最佳實踐)
7. [常見問題](#常見問題)

## 🎯 概述

本指南介紹如何在我們的 FastAPI 專案中使用依賴注入系統。經過重構後，系統具有更好的可測試性、可維護性和錯誤處理能力。

### 重構前後對比

#### ❌ 重構前（直接服務調用）
```python
@router.post("/create")
async def create_staging_task(product_name: str, source_files: List[str]):
    # 手動檢查服務可用性
    if get_file_staging_service is None:
        raise HTTPException(status_code=500, detail="檔案暫存服務未可用")
    
    # 手動獲取服務實例
    staging_service = get_file_staging_service()
    
    # 分散的錯誤處理
    try:
        result = staging_service.create_task(...)
    except Exception as e:
        logger.error(f"錯誤: {e}")
        raise HTTPException(status_code=500, detail=str(e))
```

#### ✅ 重構後（依賴注入）
```python
@router.post("/create")
async def create_staging_task(
    product_name: str,
    source_files: List[str],
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    # 自動服務檢查和錯誤處理
    # 專注於業務邏輯
    api_state.increment_request_count()
    result = staging_service.create_task(...)
    return {"success": True, "task_id": result.task_id}
```

## 🏗️ 核心概念

### 1. 服務容器 (Service Container)

服務容器負責管理所有服務實例的生命週期：

```python
from src.presentation.api.dependencies import get_service_container

# 獲取服務容器
container = get_service_container()

# 獲取服務實例
staging_service = container.get_staging_service()
processing_service = container.get_processing_service()
```

### 2. 依賴函數類型

#### 強制依賴 (`require_*`)
```python
def require_staging_service() -> FileStagingService:
    """強制依賴：服務不可用時拋出 HTTP 503"""
    service = get_service_container().get_staging_service()
    if service is None:
        raise StagingServiceUnavailableError("檔案暫存服務暫時不可用")
    return service
```

#### 可選依賴 (`get_*`)
```python
def get_staging_service() -> Optional[FileStagingService]:
    """可選依賴：允許服務不可用，返回 None"""
    return get_service_container().get_staging_service()
```

### 3. API 狀態追蹤

```python
class APIState:
    def __init__(self):
        self.total_requests = 0
        self.error_count = 0
        self.start_time = datetime.now()
    
    def increment_request_count(self):
        self.total_requests += 1
    
    def increment_error_count(self):
        self.error_count += 1
```

## 🔧 依賴注入模式

### 模式 1: 關鍵業務端點

用於必須有服務才能運作的端點：

```python
@router.post("/execute/{task_id}")
async def execute_task(
    task_id: str,
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """執行任務 - 服務不可用時自動返回 503"""
    api_state.increment_request_count()
    
    try:
        result = await staging_service.execute_task(task_id)
        return {"success": True, "result": result}
    except Exception as e:
        api_state.increment_error_count()
        raise  # 讓統一錯誤處理機制處理
```

### 模式 2: 查詢類端點

用於可以提供降級服務的端點：

```python
@router.get("/status/{task_id}")
async def get_task_status(
    task_id: str,
    staging_service: Optional[FileStagingService] = Depends(get_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """查詢任務狀態 - 支援降級模式"""
    api_state.increment_request_count()
    
    if staging_service is None:
        # 降級響應
        return {
            "success": False,
            "message": "服務暫時不可用，無法查詢詳細狀態",
            "task_id": task_id,
            "fallback_mode": True
        }
    
    status = await staging_service.get_task_status(task_id)
    return {"success": True, "status": status}
```

### 模式 3: 組合依賴

用於需要多個服務的複雜端點：

```python
@router.post("/complex-operation")
async def complex_operation(
    data: ComplexRequest,
    services: ServiceDependencies = Depends(get_all_services),
    api_state: APIState = Depends(get_api_state)
):
    """複雜操作 - 使用多個服務"""
    api_state.increment_request_count()
    
    # 檢查必需服務
    if not services.staging_service:
        raise StagingServiceUnavailableError()
    
    if not services.processing_service:
        raise ProcessingServiceUnavailableError()
    
    # 使用可選服務
    if services.search_service:
        search_results = await services.search_service.search(data.query)
    else:
        search_results = None  # 降級處理
    
    # 執行業務邏輯
    result = await complex_business_logic(services, search_results)
    return {"success": True, "result": result}
```

## ⚠️ 錯誤處理機制

### 統一錯誤處理裝飾器

```python
from src.presentation.api.error_handling import with_error_handling

@with_error_handling
async def risky_operation():
    """自動錯誤處理和日誌記錄"""
    # 可能拋出異常的操作
    pass
```

### 自定義錯誤類型

```python
from src.presentation.api.error_handling.errors import (
    StagingServiceUnavailableError,
    ProcessingServiceUnavailableError,
    ValidationError,
    BusinessLogicError
)

# 在端點中使用
if not valid_input:
    raise ValidationError("輸入參數無效", details={"field": "value"})

if business_rule_violated:
    raise BusinessLogicError("業務規則違反", recovery_strategy="MANUAL_INTERVENTION")
```

### 錯誤恢復策略

```python
from src.presentation.api.error_handling.recovery import ErrorRecoveryStrategy

# 在錯誤處理中指定恢復策略
raise StagingServiceUnavailableError(
    "服務不可用",
    recovery_strategy=ErrorRecoveryStrategy.RETRY_AFTER_DELAY,
    retry_after=30
)
```

## 🧪 測試策略

### 1. 依賴注入測試

```python
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock

from src.presentation.api.dependencies import get_staging_service

def test_create_task_with_mock_service():
    """測試使用 Mock 服務的端點"""
    
    # 創建 Mock 服務
    mock_service = Mock()
    mock_service.create_task.return_value = Mock(task_id="test-123")
    
    # 覆蓋依賴
    app.dependency_overrides[get_staging_service] = lambda: mock_service
    
    # 執行測試
    with TestClient(app) as client:
        response = client.post("/api/staging/create", params={
            "product_name": "test",
            "source_files": ["file1.txt"]
        })
    
    assert response.status_code == 200
    assert response.json()["task_id"] == "test-123"
    mock_service.create_task.assert_called_once()
```

### 2. 錯誤處理測試

```python
def test_service_unavailable_error():
    """測試服務不可用的情況"""
    
    # 模擬服務不可用
    app.dependency_overrides[get_staging_service] = lambda: None
    
    with TestClient(app) as client:
        response = client.post("/api/staging/execute/test-123")
    
    assert response.status_code == 503
    assert "SERVICE_UNAVAILABLE" in response.json()["error"]["code"]
```

### 3. 集成測試

```python
@pytest.mark.integration
async def test_full_workflow():
    """測試完整的工作流程"""
    
    # 使用真實服務進行集成測試
    async with AsyncClient(app=app, base_url="http://test") as client:
        # 創建任務
        create_response = await client.post("/api/staging/create", params={
            "product_name": "integration_test",
            "source_files": ["test_file.txt"]
        })
        
        task_id = create_response.json()["task_id"]
        
        # 執行任務
        execute_response = await client.post(f"/api/staging/execute/{task_id}")
        
        assert execute_response.status_code == 200
        assert execute_response.json()["success"] is True
```

## 🎯 最佳實踐

### 1. 端點設計原則

#### ✅ 好的做法
```python
@router.post("/create")
async def create_task(
    # 清晰的參數定義
    product_name: str = Query(..., description="產品名稱", min_length=1),
    source_files: List[str] = Query(..., description="來源檔案列表"),
    
    # 適當的依賴注入
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """清晰的文檔字符串"""
    # 業務邏輯專注且簡潔
    api_state.increment_request_count()
    result = await staging_service.create_task(product_name, source_files)
    return {"success": True, "task_id": result.task_id}
```

#### ❌ 避免的做法
```python
@router.post("/create")
async def create_task(data: dict):  # 不明確的參數類型
    # 手動服務檢查
    if get_staging_service() is None:
        raise HTTPException(500, "Service unavailable")
    
    # 混合業務邏輯和基礎設施關注點
    service = get_staging_service()
    try:
        result = service.create_task(data["name"], data["files"])
    except KeyError:
        raise HTTPException(400, "Missing required field")
    except Exception as e:
        logger.error(f"Error: {e}")
        raise HTTPException(500, str(e))
```

### 2. 依賴選擇指南

| 端點類型 | 推薦依賴 | 原因 |
|---------|---------|------|
| 創建/修改操作 | `require_*` | 必須有服務才能執行 |
| 查詢操作 | `get_*` | 可以提供降級響應 |
| 健康檢查 | `get_*` | 需要檢查服務狀態 |
| 批量操作 | `require_*` | 確保數據一致性 |

### 3. 錯誤處理指南

```python
# 參數驗證錯誤
if not product_name.strip():
    raise ValidationError("產品名稱不能為空")

# 業務邏輯錯誤
if task_already_exists:
    raise BusinessLogicError("任務已存在", details={"task_id": task_id})

# 外部服務錯誤
try:
    result = await external_service.call()
except ExternalServiceError as e:
    raise ExternalServiceError("外部服務調用失敗", service_error=e)
```

### 4. 日誌記錄最佳實踐

```python
from loguru import logger

@router.post("/create")
async def create_task(
    product_name: str,
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    # 記錄請求開始
    logger.info(f"開始創建任務: product_name={product_name}")
    
    api_state.increment_request_count()
    
    try:
        result = await staging_service.create_task(product_name)
        
        # 記錄成功
        logger.info(f"任務創建成功: task_id={result.task_id}")
        
        return {"success": True, "task_id": result.task_id}
        
    except Exception as e:
        api_state.increment_error_count()
        
        # 記錄錯誤（統一錯誤處理會處理響應）
        logger.error(f"任務創建失敗: product_name={product_name}, error={str(e)}")
        raise
```

## ❓ 常見問題

### Q1: 何時使用 `require_*` vs `get_*`？

**A**: 
- 使用 `require_*` 當操作必須有服務才能完成時
- 使用 `get_*` 當可以提供降級服務或僅查詢狀態時

### Q2: 如何處理多個服務都不可用的情況？

**A**: 使用 `get_all_services` 並檢查每個服務：

```python
services = Depends(get_all_services)
if not services.staging_service and not services.processing_service:
    raise HTTPException(503, "所有核心服務都不可用")
```

### Q3: 如何在測試中模擬服務不可用？

**A**: 使用依賴覆蓋：

```python
app.dependency_overrides[require_staging_service] = lambda: None
```

### Q4: 錯誤恢復策略如何選擇？

**A**:
- `RETRY_AFTER_DELAY`: 暫時性錯誤
- `CIRCUIT_BREAKER_OPEN`: 服務過載
- `FALLBACK_MODE`: 可降級的功能
- `MANUAL_INTERVENTION`: 需要人工處理

### Q5: 如何添加新的依賴服務？

**A**: 
1. 在服務容器中註冊服務
2. 創建依賴函數
3. 添加錯誤類型
4. 更新測試

```python
# 1. 在容器中註冊
def get_new_service() -> Optional[NewService]:
    return get_service_container().get_new_service()

def require_new_service() -> NewService:
    service = get_new_service()
    if service is None:
        raise NewServiceUnavailableError("新服務不可用")
    return service

# 2. 在端點中使用
@router.post("/new-endpoint")
async def new_endpoint(
    new_service: NewService = Depends(require_new_service)
):
    pass
```

---

**📚 延伸閱讀**:
- [FastAPI 依賴注入官方文檔](https://fastapi.tiangolo.com/tutorial/dependencies/)
- [依賴注入設計模式](https://martinfowler.com/articles/injection.html)
- [測試最佳實踐](docs/testing/testing_best_practices.md)
