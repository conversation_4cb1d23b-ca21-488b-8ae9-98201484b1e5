"""
Celery 配置文件 - 支援開發/生產模式切換

📋 配置模式：
  🧪 開發模式 (USE_MEMORY_BROKER=true) - 預設
    - 使用內存傳輸 (memory://)
    - 任務立即執行 (task_always_eager=True)
    - 不需要外部 Redis 服務
    - 適合開發和測試

  🚀 生產模式 (USE_MEMORY_BROKER=false)
    - 使用 Redis 作為消息代理
    - 真正的異步任務處理
    - 需要外部 Redis 服務和 Celery Worker
    - 適合生產環境和高負載場景

🔧 切換方式：
  - 開發模式：保持預設設定
  - 生產模式：設定環境變數 USE_MEMORY_BROKER=false
  - 或使用生產模式啟動腳本自動切換

📊 任務路由：
  - search_queue: 產品搜尋任務
  - processing_queue: CSV 摘要和程式碼比較
  - health_queue: 健康檢查
  - debug_queue: 調試任務
"""

import os

# 檢查是否使用開發模式
# 回到成功的內存模式配置
USE_MEMORY_BROKER = os.getenv('USE_MEMORY_BROKER', 'true').lower() == 'true'

if USE_MEMORY_BROKER:
    # 開發模式：使用內存傳輸，並啟用 eager 模式
    broker_url = 'memory://'
    result_backend = 'cache+memory://'
    task_always_eager = True  # 在同一進程中立即執行任務
    task_eager_propagates = True  # 傳播異常
    task_store_eager_result = True  # 【關鍵修復】即使在 eager 模式下也儲存結果
    print("🧪 Celery 使用內存傳輸和 eager 模式進行開發測試")
else:
    # 生產模式：使用真實 Redis
    broker_url = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
    result_backend = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
    task_always_eager = False
    task_store_eager_result = False  # 生產模式不需要
    print("🔴 Celery 使用真實 Redis 服務")

# 任務設定
task_serializer = 'json'
accept_content = ['json']
result_serializer = 'json'
timezone = 'Asia/Taipei'
enable_utc = True

# Worker 設定
worker_prefetch_multiplier = 1
task_acks_late = True
worker_max_tasks_per_child = 1000

# 任務路由
task_routes = {
    'tasks.search_product': {'queue': 'search_queue'},
    'tasks.run_csv_summary': {'queue': 'processing_queue'},
    'tasks.run_code_comparison': {'queue': 'processing_queue'},
    'tasks.health_check': {'queue': 'health_queue'},
    'tasks.debug_task': {'queue': 'debug_queue'},
}

# 結果過期時間
result_expires = 3600  # 1小時

# 任務追蹤
task_track_started = True
result_extended = True
