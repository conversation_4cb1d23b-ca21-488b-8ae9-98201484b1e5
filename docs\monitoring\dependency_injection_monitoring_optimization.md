# 依賴注入系統監控和日誌優化指南

**版本**: v2.0  
**更新時間**: 2025-08-02  
**狀態**: ✅ 生產就緒  

## 📋 概述

本文檔描述如何優化監控和日誌系統，確保能夠有效追蹤依賴注入系統的運作狀態、性能指標和錯誤情況。

## 🎯 監控目標

### 核心監控指標
- **依賴注入狀態**: 服務可用性、注入成功率
- **API 性能**: 響應時間、吞吐量、錯誤率
- **服務健康**: 服務實例狀態、資源使用
- **錯誤追蹤**: 錯誤類型、頻率、恢復策略
- **業務指標**: 任務成功率、處理時間

### 監控層級
1. **基礎設施層**: 容器、網路、存儲
2. **應用層**: API 端點、服務狀態
3. **業務層**: 任務執行、用戶體驗

## 📊 監控架構設計

### 監控組件架構
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   應用指標      │    │   系統指標      │    │   業務指標      │
│  (Prometheus)   │    │  (Node Exporter)│    │  (Custom)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Prometheus    │
                    │   (時序資料庫)   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │    Grafana      │
                    │   (視覺化)      │
                    └─────────────────┘
```

### 日誌架構設計
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   應用日誌      │    │   錯誤日誌      │    │   審計日誌      │
│  (Structured)   │    │  (Error Track)  │    │  (Audit Trail)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Loguru +      │
                    │   Structured    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   ELK Stack     │
                    │  (可選)         │
                    └─────────────────┘
```

## 🔧 依賴注入監控實現

### 1. 服務狀態監控

#### 服務可用性指標
```python
# src/presentation/api/monitoring/service_metrics.py
from prometheus_client import Gauge, Counter, Histogram
from typing import Dict, Any
import time

# 服務狀態指標
service_availability = Gauge(
    'dependency_injection_service_available',
    'Service availability status',
    ['service_name']
)

service_injection_total = Counter(
    'dependency_injection_total',
    'Total dependency injections',
    ['service_name', 'injection_type', 'status']
)

service_injection_duration = Histogram(
    'dependency_injection_duration_seconds',
    'Time spent on dependency injection',
    ['service_name', 'injection_type']
)

class ServiceMonitor:
    """服務監控器"""
    
    def __init__(self):
        self.service_states: Dict[str, Any] = {}
    
    def record_service_availability(self, service_name: str, available: bool):
        """記錄服務可用性"""
        service_availability.labels(service_name=service_name).set(1 if available else 0)
        self.service_states[service_name] = {
            'available': available,
            'last_check': time.time()
        }
    
    def record_injection_attempt(self, service_name: str, injection_type: str, 
                                success: bool, duration: float):
        """記錄依賴注入嘗試"""
        status = 'success' if success else 'failure'
        service_injection_total.labels(
            service_name=service_name,
            injection_type=injection_type,
            status=status
        ).inc()
        
        if success:
            service_injection_duration.labels(
                service_name=service_name,
                injection_type=injection_type
            ).observe(duration)
```

#### 依賴注入裝飾器監控
```python
# src/presentation/api/monitoring/injection_monitor.py
import functools
import time
from typing import Callable, Any
from loguru import logger

def monitor_injection(service_name: str, injection_type: str):
    """監控依賴注入的裝飾器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            success = False
            
            try:
                result = func(*args, **kwargs)
                success = True
                
                # 記錄成功的注入
                logger.info(
                    "依賴注入成功",
                    service_name=service_name,
                    injection_type=injection_type,
                    duration=time.time() - start_time
                )
                
                return result
                
            except Exception as e:
                # 記錄失敗的注入
                logger.error(
                    "依賴注入失敗",
                    service_name=service_name,
                    injection_type=injection_type,
                    error=str(e),
                    duration=time.time() - start_time
                )
                raise
                
            finally:
                # 記錄指標
                duration = time.time() - start_time
                ServiceMonitor().record_injection_attempt(
                    service_name, injection_type, success, duration
                )
        
        return wrapper
    return decorator

# 使用範例
@monitor_injection("staging_service", "require")
def require_staging_service() -> FileStagingService:
    """監控的依賴注入函數"""
    # 原有邏輯
    pass
```

### 2. API 端點監控

#### API 指標收集
```python
# src/presentation/api/monitoring/api_metrics.py
from prometheus_client import Counter, Histogram, Gauge
from fastapi import Request, Response
import time

# API 指標
api_requests_total = Counter(
    'api_requests_total',
    'Total API requests',
    ['method', 'endpoint', 'status_code']
)

api_request_duration = Histogram(
    'api_request_duration_seconds',
    'API request duration',
    ['method', 'endpoint']
)

api_active_requests = Gauge(
    'api_active_requests',
    'Currently active API requests',
    ['endpoint']
)

dependency_injection_errors = Counter(
    'dependency_injection_errors_total',
    'Total dependency injection errors',
    ['service_name', 'error_type']
)

class APIMonitoringMiddleware:
    """API 監控中間件"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        start_time = time.time()
        
        # 增加活躍請求計數
        endpoint = request.url.path
        api_active_requests.labels(endpoint=endpoint).inc()
        
        try:
            # 處理請求
            response = await self.app(scope, receive, send)
            
            # 記錄指標
            duration = time.time() - start_time
            method = request.method
            status_code = getattr(response, 'status_code', 200)
            
            api_requests_total.labels(
                method=method,
                endpoint=endpoint,
                status_code=status_code
            ).inc()
            
            api_request_duration.labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
            
        finally:
            # 減少活躍請求計數
            api_active_requests.labels(endpoint=endpoint).dec()
```

### 3. 錯誤監控和追蹤

#### 錯誤分類監控
```python
# src/presentation/api/monitoring/error_monitor.py
from prometheus_client import Counter
from typing import Dict, Any
import json

# 錯誤指標
error_total = Counter(
    'application_errors_total',
    'Total application errors',
    ['error_type', 'service_name', 'recovery_strategy']
)

class ErrorMonitor:
    """錯誤監控器"""
    
    def record_error(self, error_type: str, service_name: str = None, 
                    recovery_strategy: str = None, details: Dict[str, Any] = None):
        """記錄錯誤"""
        error_total.labels(
            error_type=error_type,
            service_name=service_name or 'unknown',
            recovery_strategy=recovery_strategy or 'none'
        ).inc()
        
        # 結構化日誌記錄
        logger.error(
            "應用錯誤",
            error_type=error_type,
            service_name=service_name,
            recovery_strategy=recovery_strategy,
            details=details or {}
        )
    
    def record_recovery_attempt(self, error_type: str, strategy: str, success: bool):
        """記錄錯誤恢復嘗試"""
        status = 'success' if success else 'failure'
        logger.info(
            "錯誤恢復嘗試",
            error_type=error_type,
            strategy=strategy,
            status=status
        )
```

## 📝 結構化日誌配置

### 1. Loguru 配置優化

#### 日誌配置
```python
# src/infrastructure/logging/config.py
from loguru import logger
import sys
import json
from typing import Dict, Any

def setup_structured_logging():
    """設置結構化日誌"""
    
    # 移除預設處理器
    logger.remove()
    
    # 控制台輸出（開發環境）
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        level="INFO",
        colorize=True,
        serialize=False
    )
    
    # 結構化日誌文件（生產環境）
    logger.add(
        "/app/logs/app/structured.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        level="INFO",
        rotation="100 MB",
        retention="30 days",
        compression="gzip",
        serialize=True  # JSON 格式
    )
    
    # 錯誤日誌文件
    logger.add(
        "/app/logs/app/error.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        level="ERROR",
        rotation="50 MB",
        retention="90 days",
        compression="gzip"
    )
    
    # 依賴注入專用日誌
    logger.add(
        "/app/logs/app/dependency_injection.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {extra[service_name]} | {message}",
        level="DEBUG",
        filter=lambda record: "service_name" in record["extra"],
        rotation="50 MB",
        retention="7 days"
    )

class StructuredLogger:
    """結構化日誌記錄器"""
    
    @staticmethod
    def log_dependency_injection(service_name: str, injection_type: str, 
                                success: bool, duration: float = None, 
                                error: str = None):
        """記錄依賴注入事件"""
        log_data = {
            "event_type": "dependency_injection",
            "service_name": service_name,
            "injection_type": injection_type,
            "success": success,
            "duration": duration,
            "error": error
        }
        
        if success:
            logger.bind(service_name=service_name).info(
                "依賴注入成功", **log_data
            )
        else:
            logger.bind(service_name=service_name).error(
                "依賴注入失敗", **log_data
            )
    
    @staticmethod
    def log_api_request(endpoint: str, method: str, status_code: int, 
                       duration: float, user_id: str = None):
        """記錄 API 請求"""
        log_data = {
            "event_type": "api_request",
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "duration": duration,
            "user_id": user_id
        }
        
        logger.info("API 請求", **log_data)
    
    @staticmethod
    def log_service_health(service_name: str, status: str, 
                          metrics: Dict[str, Any] = None):
        """記錄服務健康狀態"""
        log_data = {
            "event_type": "service_health",
            "service_name": service_name,
            "status": status,
            "metrics": metrics or {}
        }
        
        logger.info("服務健康檢查", **log_data)
```

### 2. 日誌聚合和分析

#### 日誌處理管道
```python
# src/infrastructure/logging/aggregator.py
import json
from typing import Dict, List, Any
from datetime import datetime, timedelta

class LogAggregator:
    """日誌聚合器"""
    
    def __init__(self):
        self.metrics_cache: Dict[str, Any] = {}
    
    def aggregate_dependency_injection_metrics(self, time_window: int = 300) -> Dict[str, Any]:
        """聚合依賴注入指標（5分鐘窗口）"""
        end_time = datetime.now()
        start_time = end_time - timedelta(seconds=time_window)
        
        # 從日誌文件讀取數據
        logs = self._read_logs_in_timeframe(
            "/app/logs/app/dependency_injection.log",
            start_time, end_time
        )
        
        # 聚合指標
        metrics = {
            "total_injections": len(logs),
            "success_rate": 0,
            "average_duration": 0,
            "service_breakdown": {},
            "error_types": {}
        }
        
        if logs:
            successful = [log for log in logs if log.get("success")]
            metrics["success_rate"] = len(successful) / len(logs) * 100
            
            durations = [log.get("duration", 0) for log in successful if log.get("duration")]
            if durations:
                metrics["average_duration"] = sum(durations) / len(durations)
            
            # 按服務分解
            for log in logs:
                service = log.get("service_name", "unknown")
                if service not in metrics["service_breakdown"]:
                    metrics["service_breakdown"][service] = {"total": 0, "success": 0}
                
                metrics["service_breakdown"][service]["total"] += 1
                if log.get("success"):
                    metrics["service_breakdown"][service]["success"] += 1
        
        return metrics
    
    def _read_logs_in_timeframe(self, log_file: str, start_time: datetime, 
                               end_time: datetime) -> List[Dict[str, Any]]:
        """讀取時間範圍內的日誌"""
        logs = []
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        log_time = datetime.fromisoformat(log_entry.get("time", ""))
                        
                        if start_time <= log_time <= end_time:
                            logs.append(log_entry)
                    except (json.JSONDecodeError, ValueError):
                        continue
        except FileNotFoundError:
            pass
        
        return logs
```

## 📈 Grafana 儀表板配置

### 1. 依賴注入儀表板

#### 儀表板 JSON 配置
```json
{
  "dashboard": {
    "title": "依賴注入系統監控",
    "panels": [
      {
        "title": "服務可用性",
        "type": "stat",
        "targets": [
          {
            "expr": "dependency_injection_service_available",
            "legendFormat": "{{service_name}}"
          }
        ]
      },
      {
        "title": "依賴注入成功率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(dependency_injection_total{status=\"success\"}[5m]) / rate(dependency_injection_total[5m]) * 100",
            "legendFormat": "{{service_name}}"
          }
        ]
      },
      {
        "title": "依賴注入延遲",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, dependency_injection_duration_seconds_bucket)",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, dependency_injection_duration_seconds_bucket)",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "錯誤分布",
        "type": "piechart",
        "targets": [
          {
            "expr": "sum by (error_type) (application_errors_total)",
            "legendFormat": "{{error_type}}"
          }
        ]
      }
    ]
  }
}
```

### 2. 告警規則配置

#### Prometheus 告警規則
```yaml
# prometheus/alerts/dependency_injection.yml
groups:
  - name: dependency_injection
    rules:
      - alert: ServiceUnavailable
        expr: dependency_injection_service_available == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服務 {{ $labels.service_name }} 不可用"
          description: "服務 {{ $labels.service_name }} 已經不可用超過 1 分鐘"
      
      - alert: HighInjectionFailureRate
        expr: rate(dependency_injection_total{status="failure"}[5m]) / rate(dependency_injection_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "依賴注入失敗率過高"
          description: "服務 {{ $labels.service_name }} 的依賴注入失敗率超過 10%"
      
      - alert: HighInjectionLatency
        expr: histogram_quantile(0.95, dependency_injection_duration_seconds_bucket) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "依賴注入延遲過高"
          description: "95% 的依賴注入請求延遲超過 1 秒"
```

## 🔍 監控最佳實踐

### 1. 監控策略

#### 分層監控
- **L1 - 基礎設施**: CPU、記憶體、磁碟、網路
- **L2 - 應用層**: API 響應時間、錯誤率、依賴注入狀態
- **L3 - 業務層**: 任務成功率、用戶體驗指標

#### 告警策略
- **Critical**: 服務完全不可用
- **Warning**: 性能下降或部分功能異常
- **Info**: 趨勢變化或預防性提醒

### 2. 性能優化

#### 監控開銷最小化
```python
# 使用採樣減少監控開銷
import random

class SamplingMonitor:
    def __init__(self, sample_rate: float = 0.1):
        self.sample_rate = sample_rate
    
    def should_monitor(self) -> bool:
        return random.random() < self.sample_rate
    
    def record_if_sampled(self, metric_func, *args, **kwargs):
        if self.should_monitor():
            metric_func(*args, **kwargs)
```

#### 異步日誌記錄
```python
import asyncio
from queue import Queue
import threading

class AsyncLogger:
    def __init__(self):
        self.log_queue = Queue()
        self.worker_thread = threading.Thread(target=self._log_worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
    
    def log_async(self, level: str, message: str, **kwargs):
        self.log_queue.put((level, message, kwargs))
    
    def _log_worker(self):
        while True:
            try:
                level, message, kwargs = self.log_queue.get(timeout=1)
                getattr(logger, level.lower())(message, **kwargs)
                self.log_queue.task_done()
            except:
                continue
```

---

**📊 總結**: 通過實施這套監控和日誌優化方案，可以全面追蹤依賴注入系統的運作狀態，及時發現和解決問題，確保系統穩定運行。
