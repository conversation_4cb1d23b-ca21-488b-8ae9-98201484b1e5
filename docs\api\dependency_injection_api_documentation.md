# FastAPI 依賴注入系統 API 文檔

**版本**: v2.0  
**更新時間**: 2025-08-02  
**狀態**: ✅ 生產就緒  

## 📋 概述

本文檔描述了經過依賴注入重構後的 FastAPI 系統 API。重構後的系統具有更好的可測試性、可維護性和錯誤處理能力。

### 🎯 重構亮點

- **統一依賴注入**: 所有服務通過依賴注入管理
- **統一錯誤處理**: 標準化的錯誤響應格式
- **自動服務檢查**: 服務不可用時自動返回 HTTP 503
- **請求追蹤**: 內建 API 狀態監控
- **類型安全**: 完整的 Pydantic 模型驗證

## 🔧 基礎配置

### 基礎 URL
```
http://localhost:8000
```

### 認證方式
- 無需認證（開發環境）
- Bearer Token（生產環境）

### 內容類型
```
Content-Type: application/json
```

## 📊 統一響應格式

### 成功響應
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-08-02T10:30:00Z"
}
```

### 錯誤響應
```json
{
  "success": false,
  "error": {
    "code": "SERVICE_UNAVAILABLE",
    "message": "服務暫時不可用",
    "details": {},
    "recovery_strategy": "RETRY_AFTER_DELAY"
  },
  "timestamp": "2025-08-02T10:30:00Z"
}
```

## 🏗️ 依賴注入系統

### 核心依賴

#### `require_staging_service`
- **類型**: `FileStagingService`
- **行為**: 強制依賴，服務不可用時拋出 HTTP 503
- **用途**: 關鍵業務邏輯端點

#### `get_staging_service`
- **類型**: `Optional[FileStagingService]`
- **行為**: 可選依賴，允許服務不可用
- **用途**: 查詢類端點

#### `require_processing_service`
- **類型**: `FileProcessingService`
- **行為**: 強制依賴，服務不可用時拋出 HTTP 503
- **用途**: 處理任務端點

#### `get_api_state`
- **類型**: `APIState`
- **行為**: 總是可用，用於請求追蹤
- **用途**: 所有端點的狀態監控

## 📁 暫存服務 API

### 基礎路徑: `/api/staging`

#### 1. 建立暫存任務

**端點**: `POST /api/staging/create`

**依賴注入**:
- `staging_service: FileStagingService = Depends(require_staging_service)`
- `api_state: APIState = Depends(get_api_state)`

**請求參數**:
```json
{
  "product_name": "string (必需)",
  "source_files": ["string"] (必需),
  "preserve_structure": "boolean (預設: true)",
  "use_unique_name": "boolean (預設: true)"
}
```

**成功響應** (200):
```json
{
  "success": true,
  "task_id": "uuid-string",
  "message": "暫存任務建立成功",
  "product_name": "產品名稱",
  "source_files_count": 5,
  "preserve_structure": true,
  "use_unique_name": true
}
```

**錯誤響應**:
- `400`: 參數驗證失敗
- `503`: 暫存服務不可用
- `500`: 內部服務錯誤

#### 2. 執行暫存任務

**端點**: `POST /api/staging/execute/{task_id}`

**依賴注入**:
- `staging_service: FileStagingService = Depends(require_staging_service)`
- `api_state: APIState = Depends(get_api_state)`

**路徑參數**:
- `task_id`: 任務 UUID

**成功響應** (200):
```json
{
  "success": true,
  "task_id": "uuid-string",
  "message": "暫存任務執行成功",
  "staging_path": "/path/to/staged/files",
  "files_staged": 5,
  "execution_time": 2.5
}
```

#### 3. 查詢任務狀態

**端點**: `GET /api/staging/status/{task_id}`

**依賴注入**:
- `staging_service: Optional[FileStagingService] = Depends(get_staging_service)`
- `api_state: APIState = Depends(get_api_state)`

**特殊行為**: 使用可選依賴，服務不可用時返回降級響應

**成功響應** (200):
```json
{
  "success": true,
  "task_id": "uuid-string",
  "status": "COMPLETED",
  "progress": 100,
  "created_at": "2025-08-02T10:00:00Z",
  "completed_at": "2025-08-02T10:02:30Z"
}
```

**降級響應** (503):
```json
{
  "success": false,
  "message": "暫存服務暫時不可用，無法查詢任務狀態",
  "task_id": "uuid-string",
  "fallback_mode": true
}
```

## 🔄 處理服務 API

### 基礎路徑: `/api/process`

#### 1. CSV 摘要處理（帶暫存）

**端點**: `POST /api/process/csv-summary-with-staging`

**依賴注入**:
- `processing_service: FileProcessingService = Depends(require_processing_service)`
- `api_state: APIState = Depends(get_api_state)`

**請求參數**:
```json
{
  "product_name": "string (必需)",
  "source_files": ["string"] (必需),
  "preserve_structure": "boolean (預設: true)",
  "use_unique_name": "boolean (預設: true)"
}
```

**成功響應** (200):
```json
{
  "success": true,
  "task_id": "uuid-string",
  "message": "CSV 摘要處理任務建立成功",
  "processing_type": "CSV_SUMMARY_WITH_STAGING",
  "estimated_completion": "2025-08-02T10:05:00Z"
}
```

#### 2. 程式碼比較處理（帶暫存）

**端點**: `POST /api/process/code-comparison-with-staging`

**依賴注入**:
- `processing_service: FileProcessingService = Depends(require_processing_service)`
- `api_state: APIState = Depends(get_api_state)`

**功能**: 與 CSV 摘要處理相同的模式，但執行程式碼比較分析

#### 3. 執行處理任務

**端點**: `POST /api/process/execute/{task_id}`

**依賴注入**:
- `processing_service: FileProcessingService = Depends(require_processing_service)`
- `api_state: APIState = Depends(get_api_state)`

**成功響應** (200):
```json
{
  "success": true,
  "task_id": "uuid-string",
  "message": "處理任務執行成功",
  "output_files": ["/path/to/output1.csv", "/path/to/output2.xlsx"],
  "processing_time": 45.2,
  "tool_used": "CSV_SUMMARY",
  "retries_used": 0
}
```

**失敗響應** (422):
```json
{
  "success": false,
  "task_id": "uuid-string",
  "message": "處理任務執行失敗",
  "error_message": "檔案格式不支援",
  "timeout_occurred": false,
  "cancelled": false,
  "rollback_performed": true
}
```

## ⚠️ 錯誤處理機制

### 統一錯誤類型

#### 服務不可用錯誤 (503)
```json
{
  "success": false,
  "error": {
    "code": "STAGING_SERVICE_UNAVAILABLE",
    "message": "檔案暫存服務暫時不可用",
    "recovery_strategy": "RETRY_AFTER_DELAY",
    "retry_after": 30
  }
}
```

#### 參數驗證錯誤 (400)
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "請求參數驗證失敗",
    "details": {
      "product_name": "產品名稱不能為空",
      "source_files": "至少需要一個來源檔案"
    }
  }
}
```

#### 業務邏輯錯誤 (422)
```json
{
  "success": false,
  "error": {
    "code": "BUSINESS_LOGIC_ERROR",
    "message": "業務邏輯處理失敗",
    "details": {
      "reason": "檔案格式不支援",
      "supported_formats": [".csv", ".xlsx", ".txt"]
    }
  }
}
```

### 錯誤恢復策略

- **RETRY_AFTER_DELAY**: 延遲後重試
- **CIRCUIT_BREAKER_OPEN**: 熔斷器開啟，暫停請求
- **FALLBACK_MODE**: 降級模式，提供基本功能
- **MANUAL_INTERVENTION**: 需要手動干預

## 🔍 監控和追蹤

### API 狀態端點

**端點**: `GET /api/health/status`

**響應**:
```json
{
  "api_state": {
    "total_requests": 1250,
    "successful_requests": 1180,
    "error_count": 70,
    "success_rate": 94.4,
    "uptime": "2 days, 14:30:25"
  },
  "services": {
    "staging_service": "AVAILABLE",
    "processing_service": "AVAILABLE",
    "search_service": "DEGRADED",
    "llm_service": "UNAVAILABLE"
  }
}
```

### 請求追蹤

每個 API 請求都會自動追蹤：
- 請求計數
- 錯誤計數
- 響應時間
- 服務可用性狀態

## 🚀 最佳實踐

### 客戶端實作建議

1. **錯誤處理**: 總是檢查 `success` 欄位
2. **重試邏輯**: 根據 `recovery_strategy` 實作重試
3. **降級處理**: 準備處理服務不可用的情況
4. **超時設定**: 設定合理的請求超時時間

### 範例客戶端代碼

```python
import httpx
import asyncio
from typing import Optional

class APIClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.client = httpx.AsyncClient()
    
    async def create_staging_task(self, product_name: str, source_files: list) -> dict:
        try:
            response = await self.client.post(
                f"{self.base_url}/api/staging/create",
                params={
                    "product_name": product_name,
                    "source_files": source_files
                },
                timeout=30.0
            )
            
            if response.status_code == 503:
                # 服務不可用，實作重試邏輯
                await asyncio.sleep(30)
                return await self.create_staging_task(product_name, source_files)
            
            response.raise_for_status()
            return response.json()
            
        except httpx.HTTPStatusError as e:
            # 處理 HTTP 錯誤
            error_data = e.response.json()
            raise APIError(error_data)
```

---

**📝 注意**: 本文檔描述的是重構後的 API 行為。舊版本的直接服務調用模式已被棄用。
