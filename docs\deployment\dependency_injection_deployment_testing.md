# 依賴注入系統部署測試指南

**版本**: v2.0  
**更新時間**: 2025-08-02  
**狀態**: ✅ 生產就緒  

## 📋 概述

本文檔描述如何在測試環境中驗證重構後的依賴注入系統的穩定性和性能。包含完整的部署測試流程、驗證檢查點和故障排除指南。

## 🎯 測試目標

### 主要驗證項目
- ✅ 依賴注入系統正常運作
- ✅ 統一錯誤處理機制有效
- ✅ 服務可用性檢查正確
- ✅ API 端點響應正常
- ✅ 性能指標符合預期
- ✅ 容器化部署穩定

### 測試環境要求
- **CPU**: 最少 4 核心，建議 8 核心
- **記憶體**: 最少 4GB，建議 8GB
- **磁碟**: 最少 20GB 可用空間
- **網路**: 穩定的網路連接
- **Docker**: 版本 20.10+
- **Docker Compose**: 版本 2.0+

## 🚀 部署測試流程

### 階段 1: 環境準備

#### 1.1 檢查系統要求
```bash
# 檢查 Docker 版本
docker --version
docker-compose --version

# 檢查系統資源
free -h
df -h
nproc
```

#### 1.2 準備配置文件
```bash
# 複製環境配置
cp .env.example .env.testing

# 編輯測試環境配置
cat > .env.testing << EOF
OUTLOOK_ENV=testing
PYTHONUNBUFFERED=1
PYTHONIOENCODING=utf-8
PYTHONUTF8=1
LOG_LEVEL=INFO

# 測試資料庫配置
DB_PATH=./test_data/emails.db

# 服務配置
STAGING_SERVICE_ENABLED=true
PROCESSING_SERVICE_ENABLED=true
SEARCH_SERVICE_ENABLED=false
LLM_SERVICE_ENABLED=false
EOF
```

#### 1.3 建立測試目錄結構
```bash
mkdir -p test_deployment/{logs,data,temp,config}
mkdir -p test_deployment/logs/{app,worker,system}
mkdir -p test_deployment/temp/{uploads,processing,downloads}
```

### 階段 2: 容器化部署測試

#### 2.1 構建測試鏡像
```bash
# 進入部署目錄
cd deployment

# 構建 Docker 鏡像
./deploy.bat build
# 或在 Linux/macOS
./deploy.sh build

# 驗證鏡像構建成功
docker images | grep outlook-summary
```

#### 2.2 啟動測試環境
```bash
# 使用 Docker Compose 啟動
./deploy.bat compose
# 或
./deploy.sh compose

# 檢查容器狀態
docker-compose ps
docker-compose logs -f outlook-summary
```

#### 2.3 等待服務就緒
```bash
# 等待應用啟動（約 30-60 秒）
sleep 60

# 檢查健康狀態
curl -f http://localhost:8000/health || echo "健康檢查失敗"
```

### 階段 3: 依賴注入系統驗證

#### 3.1 服務可用性測試
```bash
# 測試服務狀態端點
curl -s http://localhost:8000/api/health/status | jq '.'

# 預期響應
{
  "api_state": {
    "total_requests": 0,
    "successful_requests": 0,
    "error_count": 0,
    "success_rate": 100.0,
    "uptime": "0:01:30"
  },
  "services": {
    "staging_service": "AVAILABLE",
    "processing_service": "AVAILABLE",
    "search_service": "UNAVAILABLE",
    "llm_service": "UNAVAILABLE"
  }
}
```

#### 3.2 依賴注入端點測試
```bash
# 測試暫存服務端點（強制依賴）
curl -X POST "http://localhost:8000/api/staging/create" \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "test_product",
    "source_files": ["test1.txt", "test2.txt"],
    "preserve_structure": true,
    "use_unique_name": true
  }'

# 預期響應（成功）
{
  "success": true,
  "task_id": "uuid-string",
  "message": "暫存任務建立成功",
  "product_name": "test_product",
  "source_files_count": 2
}
```

#### 3.3 錯誤處理機制測試
```bash
# 測試參數驗證錯誤
curl -X POST "http://localhost:8000/api/staging/create" \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "",
    "source_files": []
  }'

# 預期響應（400 錯誤）
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "請求參數驗證失敗",
    "details": {
      "product_name": "產品名稱不能為空",
      "source_files": "至少需要一個來源檔案"
    }
  }
}
```

### 階段 4: 性能基準測試

#### 4.1 基本性能測試
```bash
# 安裝測試工具
pip install httpx pytest-benchmark

# 執行性能測試
python -m pytest tests/performance/ -v --benchmark-only
```

#### 4.2 負載測試
```bash
# 使用 Apache Bench 進行負載測試
ab -n 100 -c 10 http://localhost:8000/api/health/status

# 預期結果
# - 成功率: 100%
# - 平均響應時間: < 100ms
# - 95% 響應時間: < 200ms
```

#### 4.3 記憶體使用監控
```bash
# 監控容器資源使用
docker stats outlook-summary-app

# 預期指標
# - CPU 使用率: < 50%
# - 記憶體使用: < 2GB
# - 網路 I/O: 正常
```

### 階段 5: 集成測試

#### 5.1 端到端工作流程測試
```bash
# 執行完整的集成測試套件
python -m pytest tests/integration/ -v

# 測試覆蓋範圍
# - 暫存任務創建和執行
# - 處理任務創建和執行
# - 錯誤處理和恢復
# - 服務降級處理
```

#### 5.2 API 兼容性測試
```bash
# 測試所有重構後的端點
python scripts/test_api_compatibility.py

# 驗證項目
# - 所有端點正常響應
# - 響應格式符合規範
# - 錯誤處理一致
```

## 📊 驗證檢查點

### ✅ 部署成功檢查清單

#### 基礎設施檢查
- [ ] Docker 容器成功啟動
- [ ] 健康檢查端點返回 200
- [ ] 日誌文件正常生成
- [ ] 所有必需目錄已創建
- [ ] 環境變數正確設置

#### 依賴注入系統檢查
- [ ] 服務容器正常初始化
- [ ] 強制依賴正確拋出 503 錯誤（當服務不可用時）
- [ ] 可選依賴正確返回 None（當服務不可用時）
- [ ] API 狀態追蹤正常運作
- [ ] 統一錯誤處理機制有效

#### API 端點檢查
- [ ] `/api/staging/create` - 暫存任務創建
- [ ] `/api/staging/execute/{task_id}` - 暫存任務執行
- [ ] `/api/staging/status/{task_id}` - 暫存任務狀態查詢
- [ ] `/api/process/csv-summary-with-staging` - CSV 處理
- [ ] `/api/process/code-comparison-with-staging` - 程式碼比較
- [ ] `/api/process/execute/{task_id}` - 處理任務執行
- [ ] `/api/health/status` - 系統健康狀態

#### 性能指標檢查
- [ ] 應用啟動時間 < 60 秒
- [ ] API 響應時間 < 200ms (95%)
- [ ] 記憶體使用 < 2GB
- [ ] CPU 使用率 < 50%（空閒時）
- [ ] 錯誤率 < 1%

### ⚠️ 常見問題排除

#### 問題 1: 容器啟動失敗
```bash
# 檢查容器日誌
docker-compose logs outlook-summary

# 常見原因
# - 端口被佔用
# - 記憶體不足
# - 配置文件錯誤
# - 依賴服務未啟動

# 解決方案
docker-compose down
docker system prune -f
docker-compose up -d --build
```

#### 問題 2: 健康檢查失敗
```bash
# 檢查應用日誌
docker exec outlook-summary-app tail -f /app/logs/app/error.log

# 檢查網路連接
docker exec outlook-summary-app curl -f http://localhost:8000/health

# 檢查進程狀態
docker exec outlook-summary-app ps aux
```

#### 問題 3: 依賴注入錯誤
```bash
# 檢查服務初始化日誌
docker exec outlook-summary-app grep "服務初始化" /app/logs/app/app.log

# 檢查依賴配置
docker exec outlook-summary-app python -c "
from src.presentation.api.dependencies import get_service_container
container = get_service_container()
print('Staging:', container.get_staging_service())
print('Processing:', container.get_processing_service())
"
```

#### 問題 4: API 端點錯誤
```bash
# 測試特定端點
curl -v http://localhost:8000/api/staging/create

# 檢查路由註冊
docker exec outlook-summary-app python -c "
from src.presentation.api.main import app
for route in app.routes:
    print(f'{route.methods} {route.path}')
"
```

## 🔄 持續監控

### 部署後監控項目

#### 1. 應用指標監控
```bash
# 設置監控腳本
cat > monitor_deployment.sh << 'EOF'
#!/bin/bash
while true; do
    echo "=== $(date) ==="
    
    # 健康檢查
    curl -s http://localhost:8000/health | jq '.status'
    
    # 資源使用
    docker stats --no-stream outlook-summary-app
    
    # 錯誤日誌
    docker exec outlook-summary-app tail -5 /app/logs/app/error.log
    
    sleep 60
done
EOF

chmod +x monitor_deployment.sh
./monitor_deployment.sh
```

#### 2. 性能基準監控
```bash
# 定期性能測試
cat > performance_check.sh << 'EOF'
#!/bin/bash
echo "性能檢查 - $(date)"

# API 響應時間
time curl -s http://localhost:8000/api/health/status > /dev/null

# 記憶體使用
docker exec outlook-summary-app free -h

# 磁碟使用
docker exec outlook-summary-app df -h /app
EOF

chmod +x performance_check.sh
```

## 📝 測試報告模板

### 部署測試報告

**測試日期**: 2025-08-02  
**測試環境**: Docker Compose  
**測試人員**: [姓名]  

#### 測試結果摘要
- **總體狀態**: ✅ 通過 / ❌ 失敗
- **測試用例**: 25/25 通過
- **性能指標**: 符合預期
- **部署時間**: 3 分鐘
- **穩定性**: 24 小時無故障

#### 詳細測試結果
| 測試項目 | 狀態 | 響應時間 | 備註 |
|---------|------|----------|------|
| 容器啟動 | ✅ | 45s | 正常 |
| 健康檢查 | ✅ | 50ms | 正常 |
| 依賴注入 | ✅ | 80ms | 正常 |
| API 端點 | ✅ | 120ms | 正常 |
| 錯誤處理 | ✅ | 90ms | 正常 |

#### 發現的問題
1. **問題描述**: [如果有]
2. **影響程度**: 低/中/高
3. **解決方案**: [解決方法]
4. **狀態**: 已解決/待解決

#### 建議和改進
1. [改進建議 1]
2. [改進建議 2]

---

**📋 結論**: 依賴注入系統重構後的部署測試表明系統穩定、性能良好，可以安全部署到生產環境。
